import { useNotification } from "../context/notificationsContext";

export const useNotifications = () => {
	const {
		expoPushToken,
		notification,
		error,
		tokenSaveLoading,
		tokenSaveError,
		requestPermissions,
		isRequestingPermissions
	} = useNotification();

    if (error) {
        console.error('Notification error:', error.message);
    }

    if (tokenSaveError) {
        console.error('Token save error:', tokenSaveError.message);
    }

    if (notification) {
        console.log('Received notification:', JSON.stringify(notification, null, 2));
    }

	return {
		expoPushToken,
		notification,
		error,
		tokenSaveLoading,
		tokenSaveError,
		requestPermissions,
		isRequestingPermissions,
		// Convenience properties for checking states
		hasToken: !!expoPushToken,
		isTokenSaving: tokenSaveLoading,
		hasTokenSaveError: !!tokenSaveError,
		hasNotificationError: !!error,
	};
};