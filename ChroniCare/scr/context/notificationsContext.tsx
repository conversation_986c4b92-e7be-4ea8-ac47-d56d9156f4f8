import React, {
    createContext,
    useContext,
    useState,
    useEffect,
    useRef,
    ReactNode,
  } from "react";
  import * as Notifications from "expo-notifications";
  import { EventSubscription } from "expo-notifications";
  import { registerForPushNotificationsAsync } from "../utils/registerForPushNotificationsAsync";
  import { useSavePushToken } from "../hooks/useSavePushToken";
  import { useAuth } from "./authContext";
  import { useRouter } from "expo-router";
  import { useApolloClient } from "@apollo/client";
  import { GET_THREAD, GET_COMMENTS } from "../graphql/queries";
  import { Thread } from "../graphql/fragments";

  interface NotificationContextType {
    expoPushToken: string | null;
    notification: Notifications.Notification | null;
    error: Error | null;
    tokenSaveLoading: boolean;
    tokenSaveError: Error | null;
    requestPermissions: () => Promise<void>;
    isRequestingPermissions: boolean;
    newlyFetchedThread: Thread | null;
    clearNewlyFetchedThread: () => void;
  }
  
  const NotificationContext = createContext<NotificationContextType | undefined>(
    undefined
  );
  
  export const useNotification = () => {
    const context = useContext(NotificationContext);
    if (context === undefined) {
      throw new Error(
        "useNotification must be used within a NotificationProvider"
      );
    }
    return context;
  };
  
  interface NotificationProviderProps {
    children: ReactNode;
  }
  
  export const NotificationProvider: React.FC<NotificationProviderProps> = ({
    children,
  }) => {
    const [expoPushToken, setExpoPushToken] = useState<string | null>(null);
    const [notification, setNotification] =
      useState<Notifications.Notification | null>(null);
    const [error, setError] = useState<Error | null>(null);
    const [tokenSaveError, setTokenSaveError] = useState<Error | null>(null);
    const [isRequestingPermissions, setIsRequestingPermissions] = useState(false);
    const [newlyFetchedThread, setNewlyFetchedThread] = useState<Thread | null>(null);

    const router = useRouter();
    const client = useApolloClient();

    const notificationListener = useRef<EventSubscription | null>(null);
    const responseListener = useRef<EventSubscription | null>(null);
    const { user } = useAuth();
    const { savePushToken, saveLoading } = useSavePushToken();
    
    const clearNewlyFetchedThread = () => {
      setNewlyFetchedThread(null);
    };
  
    // Manual function to request permissions (called from bottom sheet)
    const requestPermissions = async () => {
      setIsRequestingPermissions(true);
      setError(null);
      setTokenSaveError(null);

      try {
        const token = await registerForPushNotificationsAsync();
        setExpoPushToken(token);

        // The token will be saved by the useEffect below when expoPushToken state is updated.
        // This avoids a double-call and simplifies logic.
        
      } catch (error) {
        console.error('Failed to register for push notifications:', error);
        setError(error as Error);
      } finally {
        setIsRequestingPermissions(false);
      }
    };

    // Set up notification listeners on mount
    useEffect(() => {
      
      notificationListener.current =
        Notifications.addNotificationReceivedListener((notification) => {
          setNotification(notification);
        });
  
      responseListener.current =
        Notifications.addNotificationResponseReceivedListener(async (response) => {
          const { data } = response.notification.request.content;
          
          if (data && data.threadId) {
            try {
              // Fetch both thread and comments data to ensure cache is up to date
              const [threadResult, commentsResult] = await Promise.allSettled([
                client.query({
                  query: GET_THREAD,
                  variables: { id: data.threadId },
                  fetchPolicy: 'network-only',
                }),
                client.query({
                  query: GET_COMMENTS,
                  variables: { threadId: data.threadId },
                  fetchPolicy: 'network-only',
                })
              ]);

              if (threadResult.status === 'fulfilled' && threadResult.value.data?.thread) {
                setNewlyFetchedThread(threadResult.value.data.thread);
              }

              router.push(`/community/${data.threadId}`);
            } catch (error) {
              console.error('Failed to pre-fetch thread data, navigating anyway.', error)
              router.push(`/community/${data.threadId}`);
            }
          }
        });
      return () => {
        if (notificationListener.current) {
          notificationListener.current.remove();
        }
        if (responseListener.current) {
          responseListener.current.remove();
        }
      };
    }, [client, router]);

    // Effect to save token when user authentication changes
    useEffect(() => {
      if (expoPushToken && user) {
        savePushToken(expoPushToken).catch((error) => {
          console.error('Failed to save push token after user auth:', error);
          setTokenSaveError(error);
        });
      }
    }, [user, expoPushToken, savePushToken]);

    return (
      <NotificationContext.Provider
        value={{
          expoPushToken,
          notification,
          error,
          tokenSaveLoading: saveLoading,
          tokenSaveError,
          requestPermissions,
          isRequestingPermissions,
          newlyFetchedThread,
          clearNewlyFetchedThread,
        }}
      >
        {children}
      </NotificationContext.Provider>
    );
  };