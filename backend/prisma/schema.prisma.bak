// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("PROD_DATABASE_URL_OLD")
  schemas  = ["identity", "user_vault", "public", "health"]
}

// =========================================
//          IDENTITY SCHEMA
// =========================================

// Main application schema - User identity data
model users {
  id             String    @id @default(cuid())
  firebaseUid    String    @unique // Firebase UID for authentication
  email          String    @unique
  displayName    String?   // Firebase displayName
  firstName      String?
  lastName       String?
  birthdate      DateTime?
  gender         String?
  countryCode    String?
  countryName    String?
  language       String?
  emailVerified  Boolean   @default(false) // Firebase emailVerified
  photoURL       String?   // Firebase photoURL
  profileImageLargeUrl    String?
  profileImageThumbnailUrl String?
  onboardingCompleted Boolean @default(false)
  creationTime   DateTime? // Firebase creationTime
  lastSignInTime DateTime? // Firebase lastSignInTime
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  consents       user_consents[]

  @@schema("identity")
}

/// Audit-log of individual user consents
model user_consents {
  id              String      @id @default(cuid())
  user            users        @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId          String
  type            ConsentType
  version         String      // e.g. "v1.0" — matches the policy/ToU version
  granted         Boolean     // true = opted in, false = withdrawn
  timestamp       DateTime    @default(now())
  ipAddress       String?     // optional
  userAgent       String?     // optional
  metadata        Json?       // e.g. { source: "mobile-signup" }
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@index([userId, type])
  @@schema("identity")
}


// =========================================
//          USER_VAULT SCHEMA
// =========================================

// User_vault schema for pseudonym mapping
model user_pseudonyms {
  id          String   @id @default(cuid())
  userId      String   @unique // Reference to User.id
  pseudonymId String   @unique @default(cuid()) // Generated pseudonym ID
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@schema("user_vault")
}

// =========================================
//          PUBLIC SCHEMA (Reference Data)
// =========================================

// Disease Definitions
model Disease {
  id                  String    @id @default(cuid())
  name                String    @unique
  displayName         String?
  description         String?
  icdCode             String?   // Optional standardized code (e.g., ICD-10/11)
  parentId            String?
  parent              Disease?  @relation("DiseaseHierarchy", fields: [parentId], references: [id])
  subDiseases         Disease[] @relation("DiseaseHierarchy")
  primaryCommunityId  String?   @unique // Optional: A disease might not have a dedicated community initially
  isActive            Boolean   @default(true)
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt

  // Relationships to other schemas/models
  primaryCommunity    Community?           @relation("PrimaryCommunityForDisease", fields: [primaryCommunityId], references: [id], onUpdate: NoAction, onDelete: SetNull)
  userDiseaseProfiles UserDiseaseProfile[] // Links to health schema
  communities         Community[]          @relation("CommunityDiseaseGroup")
  symptoms            DiseaseSymptom[]     // Symptoms associated with this disease

  @@index([name])
  @@index([icdCode])
  @@schema("public")
}

// Symptom Definitions
model Symptom {
  id          String   @id @default(cuid())
  name        String   @unique // Name of the symptom (e.g., "Fatigue", "Joint Pain")
  description String?

  // Relationships
  diseases    DiseaseSymptom[] // Diseases this symptom is associated with

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@schema("public")
}

// Join Table for Disease and Symptom
model DiseaseSymptom {
  diseaseId String // Links to public.Disease.id
  symptomId String // Links to public.Symptom.id

  disease Disease @relation(fields: [diseaseId], references: [id], onDelete: Cascade)
  symptom Symptom @relation(fields: [symptomId], references: [id], onDelete: Cascade)

  assignedAt DateTime @default(now())

  @@id([diseaseId, symptomId])
  @@schema("public")
}

model Community {
  id              String    @id @default(cuid())
  name            String    @unique
  displayName     String?
  description     String?
  diseaseGroupId  String    // Links to public.Disease.id
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  diseaseGroup    Disease   @relation("CommunityDiseaseGroup", fields: [diseaseGroupId], references: [id], onDelete: Restrict)

  // Relationships to health schema
  memberships     CommunityMembership[] // Links to health schema
  primaryForDisease Disease?            @relation("PrimaryCommunityForDisease")

  @@index([diseaseGroupId])
  @@schema("public")
}

// Medication Definitions (Master List)
model MedicationMaster {
  id             String   @id
  genericName    String   @unique
  brandNames     String[]
  localizedNames Json?
  atcCode        String?
  drugClass      String?
  drugType       String?
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships to health schema
  userMedications UserMedication[]

  @@index([genericName])
  @@schema("public")
}

// Medical Device Definitions (Master List)
model MedicalDevice {
  id             String   @id
  genericName    String   @unique
  brandNames     String[]
  localizedNames Json?
  deviceClass    String?
  description    String?
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships to health schema
  userMedicalDevices UserMedicalDevice[]

  @@index([genericName])
  @@index([deviceClass])
  @@schema("public")
}

// =========================================
//          HEALTH SCHEMA (PHI)
// =========================================

// Linking Users to Communities
model CommunityMembership {
  id            String    @id @default(cuid())
  user_healthID String    // Links to user_vault.UserPseudonym.pseudonymId
  communityId   String    // Links to public.Community.id

  community     Community @relation(fields: [communityId], references: [id], onDelete: Cascade)

  memberRole    CommunityMemberRole @default(MEMBER)
  joinedAt      DateTime  @default(now())
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@unique([user_healthID, communityId])
  @@index([user_healthID])
  @@index([communityId])
  @@schema("health")
}

// User's Relationship to a Specific Disease
model UserDiseaseProfile {
  id              String    @id @default(cuid())
  user_healthID   String    // Links to user_vault.UserPseudonym.pseudonymId
  diseaseId       String    // Links to public.Disease.id

  disease         Disease   @relation(fields: [diseaseId], references: [id], onDelete: Restrict)

  userRole        UserRole // ENUM: DIAGNOSED, UNDIAGNOSED, CAREGIVER
  isPrimary       Boolean   @default(false)
  diagnosisDate   DateTime? // Date of diagnosis (optional)

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relationships within health schema
  userMedications UserMedication[]

  @@unique([user_healthID, diseaseId])
  @@index([user_healthID])
  @@index([diseaseId])
  @@schema("health")
}

// Linking Users to Medications They Take
model UserMedication {
  id                    String    @id @default(cuid())
  user_healthID         String    // Links to user_vault.UserPseudonym.pseudonymId
  medicationMasterId    String    // Links to public.MedicationMaster.id
  userDiseaseProfileId  String?   // Links to health.UserDiseaseProfile.id

  medicationMaster      MedicationMaster    @relation(fields: [medicationMasterId], references: [id], onDelete: Restrict)
  userDiseaseProfile    UserDiseaseProfile? @relation(fields: [userDiseaseProfileId], references: [id], onDelete: SetNull)

  dosage                String?
  frequency             String?
  notes                 String?
  startDate             DateTime?
  endDate               DateTime? // When the medication was stopped
  reasonForStopping     String?   // Why the medication was stopped
  isCurrent             Boolean   @default(true)

  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  @@index([user_healthID])
  @@index([userDiseaseProfileId])
  @@index([medicationMasterId])
  @@schema("health")
}

// User Quality of Life Tracking
model QualityOfLifeLog {
  id            String    @id @default(cuid())
  user_healthID String    // Links to user_vault.UserPseudonym.pseudonymId
  score         Int       // User's self-reported score (0-100)
  notes         String?   // Optional context or notes from the user
  timestamp     DateTime  @default(now())

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@index([user_healthID, timestamp])
  @@schema("health")
}

// Linking Users to Medical Devices They Use
model UserMedicalDevice {
  user_healthID     String // Links to user_vault.UserPseudonym.pseudonymId
  medicalDeviceId   String // Links to public.MedicalDevice.id

  medicalDevice     MedicalDevice @relation(fields: [medicalDeviceId], references: [id], onDelete: Restrict)

  notes             String?   // Optional notes about device usage
  startDate         DateTime? // When the user started using the device
  endDate           DateTime? // When the user stopped using the device
  isCurrent         Boolean   @default(true)

  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@id([user_healthID, medicalDeviceId])
  @@index([user_healthID])
  @@index([medicalDeviceId])
  @@schema("health")
}

// =========================================
//                ENUMS
// =========================================

enum UserRole {
  DIAGNOSED
  UNDIAGNOSED
  CAREGIVER
  @@schema("health")
}

enum CommunityMemberRole {
  MEMBER
  MODERATOR
  ADMIN
  @@schema("health")
}


enum ConsentType {
  TERMS_OF_SERVICE
  PRIVACY_POLICY
  DATA_SHARING
  MARKETING

  @@schema("identity")
}
