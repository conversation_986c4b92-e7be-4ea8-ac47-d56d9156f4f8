import { Injectable, NotFoundException, OnModuleInit, ServiceUnavailableException, Logger } from '@nestjs/common';
import { MongoService } from '../../mongo/mongo.service';
import { Collection, ObjectId } from 'mongodb';
import { Comment, CreateCommentInput } from './dto/comments.models';
import { NotificationsService } from '../../notifications/notifications.service';
import { Thread } from '../threads/dto/threads.models';

@Injectable()
export class CommentsService implements OnModuleInit {
  private commentsCollection: Collection<Comment> | null = null;
  private threadsCollection: Collection<Thread> | null = null;
  private readonly logger = new Logger(CommentsService.name);

  constructor(
    private readonly mongoService: MongoService,
    private readonly notificationsService: NotificationsService,
  ) {}

  private environment = process.env.NODE_ENV;
  private mongoDB = this.environment === 'development' ? 'chronicare-forum-dev' : 'chronicare-forum-prod';

  onModuleInit() {
    this.logger.log('Mongo DB: ', this.mongoDB);
    const db = this.mongoService.getDatabase(this.mongoDB);
    if (db) {
      this.commentsCollection = db.collection<Comment>('Comments');
      this.threadsCollection = db.collection<Thread>('Threads');
    } else {
      this.logger.warn('Database not available. CommentsService will not be functional.');
    }
  }

  private checkDbConnection() {
    if (!this.commentsCollection) {
      throw new ServiceUnavailableException('Database service is not available.');
    }
  }

  async createComment(input: CreateCommentInput): Promise<Comment> {
    this.checkDbConnection();
    const { threadId, content, parentCommentId, author } = input;

    if (!ObjectId.isValid(threadId) || (parentCommentId && !ObjectId.isValid(parentCommentId))) {
      throw new NotFoundException('Invalid ID format for thread or parent comment.');
    }

    const newComment: Omit<Comment, '_id'> = {
      threadId: new ObjectId(threadId),
      author,
      content,
      parentCommentId: parentCommentId ? new ObjectId(parentCommentId) : undefined,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
      replyCount: 0,
      reactionCounts: {
        love: 0,
        withYou: 0,
        funny: 0,
        insightful: 0,
        poop: 0,
      },
    };

    const result = await this.commentsCollection!.insertOne(newComment as any);

    if (!result.insertedId) {
      throw new ServiceUnavailableException('Failed to create comment.');
    }

    this.logger.log(`Created new comment ${newComment.content} with ID: ${result.insertedId}`);

    // Send notification to thread author and parent comment author (don't wait for it to complete)
    this.sendCommentNotification(threadId, author.authorId, content, parentCommentId);

    return this.getCommentById(result.insertedId.toHexString());
  }

  async getCommentsForThread(threadId: string, { limit = 50, offset = 0 }): Promise<Comment[]> {
    this.checkDbConnection();
    if (!ObjectId.isValid(threadId)) {
      throw new NotFoundException('Invalid thread ID format.');
    }
    return this.commentsCollection!
      .find({ 
          threadId: new ObjectId(threadId),
      })
      .sort({ createdAt: 1 })
      .skip(offset)
      .limit(limit)
      .toArray() as Promise<Comment[]>;
  }

  async getRepliesForComment(parentCommentId: string, { limit = 10, offset = 0 }): Promise<Comment[]> {
    this.checkDbConnection();
    if (!ObjectId.isValid(parentCommentId)) {
        throw new NotFoundException('Invalid comment ID format.');
    }
    return this.commentsCollection!
      .find({ parentCommentId: new ObjectId(parentCommentId) })
      .sort({ createdAt: 1 })
      .skip(offset)
      .limit(limit)
      .toArray() as Promise<Comment[]>;
  }

  async getCommentById(commentId: string): Promise<Comment> {
    this.checkDbConnection();
    if (!ObjectId.isValid(commentId)) {
      throw new NotFoundException('Invalid comment ID format.');
    }
    const comment = await this.commentsCollection!.findOne({ _id: new ObjectId(commentId) });
    if (!comment) {
      throw new NotFoundException(`Comment with ID ${commentId} not found.`);
    }
    return comment;
  }

  private async sendCommentNotification(
    threadId: string, 
    commentAuthorId: string, 
    commentContent: string,
    parentCommentId?: string
  ): Promise<void> {
    try {
      // Get the thread information
      const thread = await this.threadsCollection!.findOne({ _id: new ObjectId(threadId) });
      if (!thread) {
        this.logger.warn(`Thread with ID ${threadId} not found. Skipping notification.`);
        return;
      }

      // Send notification to thread author (if not the comment author)
      if (thread.author.authorId !== commentAuthorId) {
        await this.notificationsService.sendNewCommentNotification(
          threadId,
          thread.communityId,
          commentAuthorId,
          thread.author.authorId,
          thread.title,
          commentContent,
        );
      }

      // Send notification to parent comment author if this is a reply
      if (parentCommentId) {
        const parentComment = await this.commentsCollection!.findOne({ _id: new ObjectId(parentCommentId) });
        if (parentComment) {
          // Don't send notification if the reply author is the same as the parent comment author
          // or if the parent comment author is the same as the thread author (they already got a notification)
          if (parentComment.author.authorId !== commentAuthorId && 
              parentComment.author.authorId !== thread.author.authorId) {
            await this.notificationsService.sendNewReplyNotification(
              threadId,
              thread.communityId,
              commentAuthorId,
              parentComment.author.authorId,
              thread.title,
              commentContent,
            );
          }
        }
      }
    } catch (error) {
      this.logger.error(`Failed to send comment notification for thread ${threadId}:`, error);
      // Don't throw the error as we don't want to fail comment creation due to notification issues
    }
  }
}
