import { Injectable, NotFoundException, OnModuleInit, ServiceUnavailableException, Logger } from '@nestjs/common';
import { MongoService } from '../../mongo/mongo.service';
import { Collection, ObjectId } from 'mongodb';
import { Thread, CreateThreadInput } from './dto/threads.models';
import { NotificationsService } from '../../notifications/notifications.service';

@Injectable()
export class ThreadsService implements OnModuleInit {
  private threadsCollection: Collection<Thread> | null = null;
  private readonly logger = new Logger(ThreadsService.name);

  constructor(
    private readonly mongoService: MongoService,
    private readonly notificationsService: NotificationsService,
  ) {}

  private environment = process.env.NODE_ENV;
  private mongoDB = this.environment === 'production' ? 'chronicare-forum-prod' : 'chronicare-forum-dev';

  onModuleInit() {
    this.logger.log('Mongo DB: ', this.mongoDB);
    const db = this.mongoService.getDatabase(this.mongoDB);
    if (db) {
      this.threadsCollection = db.collection<Thread>('Threads');
    } else {
      this.logger.warn('Database not available. ThreadsService will not be functional.'); 
    }
  }

  private checkDbConnection() {
    if (!this.threadsCollection) {
      throw new ServiceUnavailableException('Database service is not available.');
    }
  }

  async getThreads({ limit = 10, offset = 0, communityIds, cursor, since }: { limit?: number; offset?: number; communityIds?: string[]; cursor?: string, since?: string }): Promise<Thread[]> {
    this.checkDbConnection();
    this.logger.log('getThreads', { limit, offset, communityIds, cursor, since });
    // Build base query for community filtering
    let query: any = communityIds && communityIds.length > 0 ? { communityId: { $in: communityIds } } : {};
    this.logger.log(`Constructed query: ${JSON.stringify(query)}`);

    // Add 'since' filter if provided
    if (since) {
      query.createdAt = { $gt: new Date(since) };
      // When using 'since', we typically want the newest, so we don't use cursor or offset
      return this.threadsCollection!
        .find(query)
        .sort({ createdAt: -1, _id: -1 })
        .limit(limit)
        .toArray() as Promise<Thread[]>;
    }

    // If cursor is provided, use cursor-based pagination instead of offset
    if (cursor) {
      if (!ObjectId.isValid(cursor)) {
        this.logger.warn(`Invalid cursor format: ${cursor}`);
        return [];
      }

      // For cursor-based pagination, get threads created before the cursor thread
      // Since we sort by createdAt descending, we want threads with _id < cursor
      query._id = { $lt: new ObjectId(cursor) };
      
      // When using cursor, ignore offset
      return this.threadsCollection!
        .find(query)
        .sort({ createdAt: -1, _id: -1 }) // Secondary sort by _id for consistency
        .limit(limit)
        .toArray() as Promise<Thread[]>;
    }
    
    // Fallback to offset-based pagination when no cursor is provided
    return this.threadsCollection!
      .find(query)
      .sort({ createdAt: -1, _id: -1 })
      .skip(offset)
      .limit(limit)
      .toArray() as Promise<Thread[]>;
  }

  async getThreadById(id: string): Promise<Thread> {
    this.checkDbConnection();
    if (!ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid thread ID format.');
    }
    const thread = await this.threadsCollection!.findOne({ _id: new ObjectId(id) });
    if (!thread) {
      throw new NotFoundException(`Thread with ID ${id} not found.`);
    }
    return thread as Thread;
  }

  async createThread(createThreadInput: CreateThreadInput): Promise<Thread> {
    this.checkDbConnection();
    
    const newThread = {
      communityId: createThreadInput.communityId,
      author: createThreadInput.author,
      title: createThreadInput.title,
      content: createThreadInput.content,
      labels: createThreadInput.labels || [],
      imageUrls: createThreadInput.imageUrls || [],
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
      commentCount: 0,
      reactionCounts: {
        love: 0,
        withYou: 0,
        funny: 0,
        insightful: 0,
        poop: 0,
      },
    };
    const result = await this.threadsCollection!.insertOne(newThread as any);
    
    if (!result.insertedId) {
      throw new ServiceUnavailableException('Failed to create thread.');
    }

    this.logger.log(`Created new thread with ID: ${result.insertedId}`);
    
    const createdThread = {
      ...newThread,
      _id: result.insertedId,
    };

    this.notificationsService.sendNewThreadNotification(
      createdThread._id.toHexString(),
      createdThread.communityId,
      createdThread.author.authorId,
      createdThread.title,
      createdThread.content,
    );
    
    // Return the created thread object without an extra DB call.
    // We cast it to Thread as it's structurally compatible.
    return createdThread as Thread;
  }

  async incrementCommentCount(threadId: string): Promise<void> {
    this.checkDbConnection();
    if (!ObjectId.isValid(threadId)) {
      throw new NotFoundException('Invalid thread ID format.');
    }
    
    await this.threadsCollection!.updateOne(
      { _id: new ObjectId(threadId) },
      { $inc: { commentCount: 1 }, $set: { updatedAt: new Date() } }
    );
  }

  async decrementCommentCount(threadId: string): Promise<void> {
    this.checkDbConnection();
    if (!ObjectId.isValid(threadId)) {
      throw new NotFoundException('Invalid thread ID format.');
    }
    
    await this.threadsCollection!.updateOne(
      { _id: new ObjectId(threadId) },
      { $inc: { commentCount: -1 }, $set: { updatedAt: new Date() } }
    );
  }
}
