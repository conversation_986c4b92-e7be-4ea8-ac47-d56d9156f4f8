import { Module, forwardRef } from '@nestjs/common';
import { ThreadsService } from './threads.service';
import { MongoModule } from '../../mongo/mongo.module';
import { NotificationsModule } from '../../notifications/notifications.module';

@Module({
  imports: [MongoModule, forwardRef(() => NotificationsModule)],
  providers: [ThreadsService],
  exports: [ThreadsService],
})
export class ThreadsModule {}