import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { SavePushTokenInput } from './dto/save-push-token.input';
import { HttpService } from '@nestjs/axios';
import { CommunityService } from '../community/community.service';
import { AxiosError } from 'axios';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);

  constructor(
    private prisma: PrismaService,
    private readonly httpService: HttpService,
    private readonly communityService: CommunityService,
  ) {}

  async sendNewThreadNotification(
    threadId: string,
    communityId: string,
    authorId: string,
    threadTitle: string,
    threadContent: string,
  ) {
    this.logger.log(`New thread notification process started for community ${communityId}`);

    try {
      const community = await this.communityService.getCommunityById(communityId);
      if (!community) {
        this.logger.warn(`Community with ID ${communityId} not found. Aborting notification.`);
        return;
      }

      const pushTokens: { token: string }[] = await this.prisma.client.$queryRaw`
        SELECT t.token
        FROM "identity".push_tokens AS t
        INNER JOIN "user_vault".user_pseudonyms AS p ON t."userId" = p."userId"
        INNER JOIN "health"."CommunityMembership" AS cm ON p."pseudonymId" = cm."user_healthID"
        WHERE cm."communityId" = ${communityId} AND t."userId" != ${authorId}
      `;

      const tokens = pushTokens.map(t => t.token);
      if (tokens.length === 0) {
        this.logger.log('No push tokens found for users in the community.');
        return;
      }

      console.log('Push tokens: ', pushTokens);

      const notificationPayload = {
        to: tokens,
        //title: `New Post in ${community.displayName}`,
        title: 'New post in your community!',
        body: `${threadTitle}:\n${threadContent}`,
        data: { threadId },
      };

      await this.httpService.axiosRef.post('https://exp.host/--/api/v2/push/send', notificationPayload, {
        headers: {
          'host': 'exp.host',
          'accept': 'application/json',
          'accept-encoding': 'gzip, deflate',
          'content-type': 'application/json',
        },
      });

      console.log(notificationPayload);
      
      this.logger.log(`Successfully sent notifications for new thread ${threadId}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error('Error sending push notifications:', error.response?.data);
      } else {
        this.logger.error('An unexpected error occurred during new thread notification:', error);
      }
    }
  }

  sendNewCommentNotification(
    threadId: string,
    communityId: string,
    authorId: string,
    threadTitle: string,
    threadContent: string,
  ) {}


  async savePushToken(userId: string, input: SavePushTokenInput): Promise<boolean> {
    try {
      this.logger.log(`Saving push token for user ${userId}`);

      // Use upsert to handle both new tokens and updates
      // This ensures we don't create duplicates and handle token updates properly
      await this.prisma.client.push_tokens.upsert({
        where: {
          token: input.token,
        },
        update: {
          userId: userId,
          updatedAt: new Date(),
        },
        create: {
          token: input.token,
          userId: userId,
        },
      });

      this.logger.log(`Successfully saved push token for user ${userId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to save push token for user ${userId}:`, error);
      throw error;
    }
  }

  async removePushToken(token: string): Promise<boolean> {
    try {
      this.logger.log(`Removing push token: ${token.substring(0, 10)}...`);

      await this.prisma.client.push_tokens.delete({
        where: {
          token: token,
        },
      });

      this.logger.log(`Successfully removed push token`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to remove push token:`, error);
      throw error;
    }
  }

  async getUserPushTokens(userId: string): Promise<string[]> {
    try {
      const tokens = await this.prisma.client.push_tokens.findMany({
        where: {
          userId: userId,
        },
        select: {
          token: true,
        },
      });

      return tokens.map(t => t.token);
    } catch (error) {
      this.logger.error(`Failed to get push tokens for user ${userId}:`, error);
      throw error;
    }
  }
}
